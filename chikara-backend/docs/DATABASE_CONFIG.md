# Database-Aware Game Configuration

The game configuration system has been updated to support storing configuration values in the database while maintaining backward compatibility with the existing code.

## Overview

The system now supports:
- **Default configurations** stored in code (fallback values)
- **Database overrides** stored in the `game_config` table
- **In-memory caching** for performance (5-minute cache)
- **Backward compatibility** with existing code

## Database Schema

The `game_config` table has the following structure:
```sql
CREATE TABLE game_config (
  id INT PRIMARY KEY AUTO_INCREMENT,
  key VARCHAR(255) UNIQUE NOT NULL,
  value JSON NOT NULL,
  category VARCHAR(100),
  isPublic BOOLEAN DEFAULT TRUE,
  createdAt DATETIME DEFAULT NOW(),
  updatedAt DATETIME DEFAULT NOW() ON UPDATE NOW()
);
```

## Usage

### Synchronous Access (Backward Compatible)

```typescript
import gameConfig from "../config/gameConfig.js";

// This still works and returns default values
const pvpMinLevel = gameConfig.PVP_MIN_LVL;
```

### Database-Aware Access (Recommended)

```typescript
import { gameConfig, getDatabaseAwareFlatConfig } from "../config/gameConfig.js";

// Get database-aware config object
const dbConfig = await gameConfig.getDatabaseAware();
const pvpMinLevel = dbConfig.battleConfig.public.PVP_MIN_LVL;

// Get flat config with database overrides
const flatConfig = await getDatabaseAwareFlatConfig();
const pvpMinLevel = flatConfig.PVP_MIN_LVL;
```

### Public Config (For Frontend)

```typescript
import { getDatabaseAwarePublicConfig } from "../config/gameConfig.js";

// Get only public config values (excludes hidden configs)
const publicConfig = await getDatabaseAwarePublicConfig();
```

## Configuration Keys

Configuration keys follow the pattern: `{category}.{configName}`

Examples:
- `battle.PVP_MIN_LVL`
- `auth.REGISTRATION_DISABLED`
- `user.MAX_LEVEL_CAP`

## Managing Configuration

### Seeding Initial Values

```bash
# Seed database with all default configuration values
bun run src/scripts/seedGameConfig.ts
```

### Updating Configuration

```typescript
import { db } from "../lib/db.js";
import { clearConfigCache } from "../config/gameConfig.js";

// Update a config value
await db.game_config.upsert({
    where: { key: "battle.PVP_MIN_LVL" },
    update: { value: 10 },
    create: {
        key: "battle.PVP_MIN_LVL",
        value: 10,
        category: "battle",
        isPublic: true,
    },
});

// Clear cache to force reload
clearConfigCache();
```

### Cache Management

```typescript
import { clearConfigCache } from "../config/gameConfig.js";

// Clear the configuration cache
clearConfigCache();
```

## Performance

- **In-memory caching**: Configurations are cached for 5 minutes
- **Fallback to defaults**: If database is unavailable, defaults are used
- **Minimal overhead**: Database queries only happen on cache miss

## Migration Strategy

1. **Phase 1**: Deploy the new system (current)
   - All existing code continues to work
   - Database overrides are available but optional

2. **Phase 2**: Gradually migrate critical configs
   - Update code to use database-aware methods
   - Add admin interface for config management

3. **Phase 3**: Full migration
   - All configs use database-aware methods
   - Remove synchronous fallbacks if desired

## Testing

```bash
# Test the configuration system
bun run src/scripts/testGameConfig.ts
```

## Best Practices

1. **Use database-aware methods** for new code
2. **Keep defaults in code** as fallback values
3. **Clear cache** after updating configs
4. **Use categories** to organize related configs
5. **Mark sensitive configs** as `isPublic: false`
