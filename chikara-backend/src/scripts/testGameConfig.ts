#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to test the database-aware game configuration system
 * Usage: bun run src/scripts/testGameConfig.ts
 */

import { 
    gameConfig, 
    getDatabaseAwareFlatConfig, 
    getDatabaseAwarePublicConfig,
    clearConfigCache 
} from "../config/gameConfig.js";
import { db } from "../lib/db.js";
import { logger } from "../utils/log.js";

async function main() {
    try {
        logger.info("Testing database-aware game config system...");
        
        // Test 1: Get default config (synchronous)
        logger.info("=== Test 1: Default Config ===");
        logger.info("Default PVP_MIN_LVL:", gameConfig.battleConfig.public.PVP_MIN_LVL);
        
        // Test 2: Get database-aware config
        logger.info("=== Test 2: Database-Aware Config ===");
        const dbAwareConfig = await gameConfig.getDatabaseAware();
        logger.info("Database-aware PVP_MIN_LVL:", dbAwareConfig.battleConfig.public.PVP_MIN_LVL);
        
        // Test 3: Update a config value in database
        logger.info("=== Test 3: Update Config in Database ===");
        await db.game_config.upsert({
            where: { key: "battle.PVP_MIN_LVL" },
            update: { value: 10 },
            create: {
                key: "battle.PVP_MIN_LVL",
                value: 10,
                category: "battle",
                isPublic: true,
            },
        });
        logger.info("Updated PVP_MIN_LVL to 10 in database");
        
        // Test 4: Clear cache and get updated config
        logger.info("=== Test 4: Get Updated Config ===");
        clearConfigCache();
        const updatedConfig = await gameConfig.getDatabaseAware();
        logger.info("Updated database-aware PVP_MIN_LVL:", updatedConfig.battleConfig.public.PVP_MIN_LVL);
        
        // Test 5: Test flat config
        logger.info("=== Test 5: Flat Config ===");
        const flatConfig = await getDatabaseAwareFlatConfig();
        logger.info("Flat config PVP_MIN_LVL:", flatConfig.PVP_MIN_LVL);
        
        // Test 6: Test public config
        logger.info("=== Test 6: Public Config ===");
        const publicConfig = await getDatabaseAwarePublicConfig();
        logger.info("Public config PVP_MIN_LVL:", publicConfig.PVP_MIN_LVL);
        
        // Test 7: Verify hidden configs are not in public
        logger.info("=== Test 7: Hidden Config Exclusion ===");
        logger.info("SITE_MAINTENANCE_MODE in public config:", 'SITE_MAINTENANCE_MODE' in publicConfig);
        logger.info("SITE_MAINTENANCE_MODE in flat config:", 'SITE_MAINTENANCE_MODE' in flatConfig);
        
        logger.info("All tests completed successfully!");
        
        process.exit(0);
    } catch (error) {
        logger.error("Test failed:", error);
        process.exit(1);
    }
}

// Run the script if called directly
if (import.meta.main) {
    main();
}
