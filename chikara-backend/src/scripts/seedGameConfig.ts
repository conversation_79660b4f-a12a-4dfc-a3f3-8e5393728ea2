#!/usr/bin/env bun

/**
 * <PERSON><PERSON>t to seed the database with game configuration values
 * Usage: bun run src/scripts/seedGameConfig.ts
 */

import { seedDatabaseConfig } from "../config/gameConfig.js";
import { logger } from "../utils/log.js";

async function main() {
    try {
        logger.info("Starting game config seeding...");
        
        const count = await seedDatabaseConfig();
        
        logger.info(`Successfully seeded ${count} configuration entries`);
        logger.info("Game config seeding completed!");
        
        process.exit(0);
    } catch (error) {
        logger.error("Failed to seed game config:", error);
        process.exit(1);
    }
}

// Run the script if called directly
if (import.meta.main) {
    main();
}
