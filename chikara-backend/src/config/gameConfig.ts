import ms from "ms";
import { db } from "../lib/db.js";
import { createCache } from "../utils/cacheHelper.js";
import { logger, LogErrorStack } from "../utils/log.js";

// In-memory cache for game config
const configCache = createCache<Record<string, any>>("5m"); // 5 minute cache

// Default configurations (fallback values)
// AUTH //
const authConfig = {
    public: {
        REGISTRATION_DISABLED: false,
        LOGIN_DISABLED: false,
        DISCORD_AUTH_ENABLED: true,
        GOOGLE_AUTH_ENABLED: true,
    },
    hidden: {
        RESET_TOKEN_VALID_FOR: ms("10m"),
        SITE_MAINTENANCE_MODE: false,
        REGISTRATION_CHATMESSAGE_DISABLED: false,
    },
} as const;

// FRONTEND CONFIG //
const frontendConfig = {
    public: {
        MARQUEE_BANNER_DISABLED: false,
    },
    hidden: {},
} as const;

// BATTLE //
const battleConfig = {
    public: {
        PVP_MIN_LVL: 5,
        PVP_BATTLE_AP_COST: 2,
        ROOFTOP_BATTLE_AP_COST: 2,
        MUG_XP: 100,
        CRIPPLE_XP: 100,
        LEAVE_XP: 200,
        NPC_KILL_XP: 100,
        BOSS_KILL_XP: 200,
        DAILY_USER_ATTACK_LIMIT: 3,
        BASE_STAMINA: 100,
    },
    hidden: {
        BATTLE_TIMEOUT_MS: ms("10m"), // 10 mins
        FLEE_CHANCE: 0.4,
        BASE_JAIL_CHANCE: 0.3,
        JAIL_DURATION_MS: ms("10m"),
        BASE_MUG_AMOUNT: Math.random() * (0.5 - 0.3) + 0.3,
        BASE_STAMINA_REGEN: 5,
        DEFAULT_HOSPITAL_DURATION_MS: ms("10m"), // NPC loss/Leave
        MUG_HOSPITAL_DURATION_MS: ms("15m"),
        PVP_LOSS_HOSPITAL_DURATION_MS: ms("15m"),
        CRIPPLE_HOSPITAL_DURATION_MS: ms("30m"),
    },
} as const;

// USER //
const userConfig = {
    public: {
        MAX_LEVEL_CAP: 40,
        HEALING_TICK_INTERVAL: ms("1m"), // every minute
        AP_TICK_INTERVAL: ms("10m"), // every 10 minutes
        AP_TICK_INTERVAL_MINUTES: 10, // every 10 minutes
        ENERGY_TICK_MS: ms("1m"), // 1 energy per minute
        HEALTH_REGEN_AMOUNT: 0.025, // 2.5% max hp
        // Focus system
        FOCUS_PER_BATTLE_WIN: 1, // Focus gained from winning battles
        FOCUS_PER_NPC_KILL: 1, // Focus gained from killing NPCs (same as battle win)
        FOCUS_PER_QUEST_COMPLETE: 5, // Focus gained from completing quests
        FOCUS_PER_DAILY_QUEST: 3, // Focus gained from daily quests
        FOCUS_PER_MISSION_HOUR: 1, // Focus gained per mission hour
        FOCUS_PER_EXPLORE_NODE: 3, // Focus gained from exploring nodes
        DAILY_FATIGUE_CAP: 200, // Maximum fatigue that can be spent per day
        FOCUS_TO_EXP_RATIO: 10, // 1 focus = 10 exp when training
        TALENT_RESPEC_BASE_COST: 1000,
        NEW_PLAYER_HOSPITAL_PROTECTION_LEVEL: 15,
    },
    hidden: {
        XP_TO_LEVEL_MULTIPLIER: 800,
    },
} as const;

// USER SKILLS (STATS) //
const skillsConfig = {
    public: {
        STAMINA_PER_ENDURANCE_LEVEL: 5,
        STA_STAT_EFFECT_MAX_COMBAT_STA_INCREASE: 5,
    },
    hidden: {},
} as const;

// BANK //
const bankConfig = {
    public: {
        DEPOSIT_DISABLED: false,
        BANK_DISABLED: false,
        TRANSACTION_FEE: 0.15,
        MINIMUM_DEPOSIT: 300,
        MINIMUM_WITHDRAWAL: 100,
        MINIMUM_TRANSFER: 100,
        TRANSACTION_HISTORY_LIMIT: 10,
    },
    hidden: {},
} as const;

// CASINO //
const casinoConfig = {
    public: { CASINO_DISABLED: false, LOTTERY_DISABLED: false, SLOTS_MAX_BET: 5_000_000, LOTTERY_TICKET_COST: 2500 },
    hidden: {},
} as const;

// JOBS //
const jobsConfig = {
    public: { JOBS_DISABLED: false },
    hidden: {},
} as const;

// LEADERBOARDS //
const leaderboardsConfig = {
    public: { LEADERBOARDS_DISABLED: false, USERS_PER_BOARD: 3 },
    hidden: {},
} as const;

// CRAFTING //
const craftingConfig = {
    public: { CRAFTING_ENERGY_COST: 0 },
    hidden: {},
} as const;

// CHAT //
const chatConfig = {
    public: { CHAT_DISABLED: false, CHAT_MESSAGE_SENDING_DISABLED: false },
    hidden: { MAX_CHAT_HISTORY_LENGTH: 300, DISCORD_CHAT_WEBHOOK_ENABLED: true },
} as const;

// NOTIFICATIONS //
const notificationConfig = {
    public: {},
    hidden: {
        MAX_NOTIFICATION_HISTORY_LENGTH: 300,
        AP_NOTIFICATIONS_ENABLED: true,
        ENERGY_NOTIFICATIONS_ENABLED: false,
        HEALTH_NOTIFICATIONS_ENABLED: true,
    },
} as const;

// UNIQUE ITEMS //
const uniqueItemsConfig = {
    public: {
        ANON_ITEM_NAME: "Balaclava",
        HOSPITALISE_ITEM_NAME: "Death Book",
        REVIVE_ITEM_NAME: "Life Book",
        JAIL_ITEM_NAME: "Kompromat",
        MEGAPHONE_ITEM_NAME: "Megaphone",
        GANG_CREATION_ITEM_NAME: "Gang Sigil",
        DAILY_CHEST_ITEM_ID: 232,
        RUNSHOES_ITEM_NAME: "Shoes of Quickness",
        WEALTH_RING_ITEM_NAME: "Ring of Wealth",
        SM_RAW_MATERIALS_ITEM_NAME: "Small Raw Materials Crate",
        SM_TOOLS_ITEM_NAME: "Small Tools Crate",
    },
    hidden: {
        // 30 mins
        DEATHNOTE_HOSPITAL_TIME_MS: ms("30m"),
        // 20 mins
        KOMPROMAT_JAIL_TIME_MS: ms("20m"),
    },
} as const;

// LEVEL GATES //
const levelGatesConfig = {
    public: {
        JOBS_LEVEL_GATE: 4,
        TALENTS_LEVEL_GATE: 5,
        CRAFTING_LEVEL_GATE: 7,
        SHOP1_LEVEL_GATE: 7,
        SHOP2_LEVEL_GATE: 15,
        SHOP3_LEVEL_GATE: 0,
        COURSES_LEVEL_GATE: 8,
        DAILY_QUESTS_LEVEL_GATE: 3,
        ROOFTOP_BATTLES_LEVEL_GATE: 10,
        MARKET_LEVEL_GATE: 12,
        ARCADE_LEVEL_GATE: 15,
    },
    hidden: {},
} as const;

// ROGUELIKE //
const roguelikeConfig = {
    public: {
        ROGUELIKE_DISABLED: false,
        ACTION_POINTS_REQUIRED: 1,
        CHURCH_MINIMUM_ZONE_LVL: 3,
        MALL_MINIMUM_ZONE_LVL: 7,
        SHRINE_MINIMUM_ZONE_LVL: 10,
        ALLEY_MINIMUM_ZONE_LVL: 16,
        SEWERS_MINIMUM_ZONE_LVL: 12,
    },
    hidden: {
        // 10 Mins
        DEFAULT_ENCOUNTER_HOSPITAL_DURATION_MS: ms("10m"),
        DEFAULT_ENCOUNTER_JAIL_DURATION_MS: ms("10m"),
        NORMAL_NPC_BATTLE_TIMEOUT_MS: ms("10m"),
        BOSS_BATTLE_TIMEOUT_MS: ms("10m"),
        SCAVENGE_TIMEOUT_MS: ms("5m"),
        MAX_NODES: 20,
        MIN_NODES: 18,
        DOUBLE_EDGE_CHANCE: 0.3,
        MAX_BUFF_VALUE_CAP: 1.5,
        BATTLE_WEIGHT: 9,
        BUFF_WEIGHT: 1,
        CHARACTER_WEIGHT: 5,
        SCAVENGE_NODE_WEIGHT: 4.5,
    },
} as const;

// CLASSES //
const classesConfig = {
    public: { CLASS_NAMES: ["Honoo", "Mizu", "Tsuchi", "Kaze"] },
    hidden: {},
} as const;

const registrationCodes = {
    public: { REGISTRATION_CODES_DISABLED: false },
    hidden: { REFERRAL_CODE_LIMIT: 3 },
} as const;

// TASKS/QUESTS //
const questsConfig = {
    public: { QUESTS_DISABLED: false },
    hidden: {
        QUEST_XP_REWARD_MULTIPLIER: 0.25,
        DAILY_QUEST_XP_REWARD_MULTIPLIER: 0.1,
    },
} as const;

// SHOPS //
const shopConfig = {
    public: { MAX_TRADER_REP: 3, SHOP_ITEM_COST_MULTIPLIER: 7, LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT: 4 },
    hidden: {},
} as const;

// BOUNTIES //
const bountyConfig = {
    public: {
        MIN_BOUNTY: 100,
        BOUNTY_FEE: 0.1,
        BOUNTY_MIN_LEVEL: 5,
        DEFAULT_STAFF_BOUNTY_USER_ID: 5,
        RANDOM_BOUNTY_AMOUNT: 50,
    },
    hidden: {
        RANDOM_BOUNTY_CHANCE: 0.2,
    },
} as const;

// PROFILE //
const profileConfig = {
    public: { PROFILE_COMMENT_MAX_LENGTH: 160 },
    hidden: {},
} as const;

// MISSIONS //
const missionConfig = {
    public: {
        MISSION_TIER_REQ_LEVELS: [4, 10, 15, 20, 25],
        MISSION_TIER_REQ_HOURS: [0, 15, 40, 80, 125],
        MISSION_DURATIONS_HOURS: [2, 4, 8],
    },
    hidden: {},
} as const;

const shrineConfig = {
    public: {
        SHRINE_DISABLED: false,
        SHRINE_MINIMUM_DONATION: 100,
    },
    hidden: {
        SHRINE_GOAL_CIRCULATING_PERCENT: 0.07,
    },
} as const;

const auctionConfig = {
    public: {
        ALLOWED_AUCTION_ITEM_TYPES: [
            "weapon",
            "offhand",
            "ranged",
            "shield",
            "head",
            "finger",
            "hands",
            "chest",
            "legs",
            "feet",
            "consumable",
            "junk",
            "special",
            "recipe",
            "crafting",
            "pet",
            "pet_food",
        ],
        BLACKLISTED_AUCTION_ITEM_TYPES: ["quest"],
        BLACKLISTED_AUCTION_ITEM_IDS: [232, 37, 22, 155, 250, 247, 248, 246, 107, 108, 244],
    },
    hidden: {},
} as const;

const gangConfig = {
    public: {
        DAILY_ESSENCE_CAP: 100,
    },
    hidden: {
        ELO_K_FACTOR: 32,
        BASE_LIFE_ESSENCE_REWARD: 5,
        BASE_RESPECT_REWARD: 25,
    },
} as const;

const infirmaryConfig = {
    public: {
        MINOR_COST_PER_LEVEL: 125,
        MODERATE_COST_PER_LEVEL: 250,
        SEVERE_COST_PER_LEVEL: 450,
        COST_PER_HP: 4,
    },
    hidden: {},
} as const;

const petConfig = {
    public: {
        EGG_TIME_PER_PROGRESS_POINT: ms("1h"),
    },
    hidden: {},
} as const;

/**
 * Loads configuration from database and merges with defaults
 */
async function loadConfigFromDatabase(): Promise<Record<string, any>> {
    try {
        const dbConfigs = await db.game_config.findMany();
        const configOverrides: Record<string, any> = {};

        for (const config of dbConfigs) {
            configOverrides[config.key] = config.value;
        }

        return configOverrides;
    } catch (error) {
        LogErrorStack({ message: "Failed to load config from database, using defaults", error });
        return {};
    }
}

/**
 * Gets configuration value with database override support
 */
async function getConfigValue(key: string, defaultValue: any): Promise<any> {
    try {
        // Check cache first
        if (configCache.isValid()) {
            const cachedConfig = configCache.get().data;
            if (cachedConfig && key in cachedConfig) {
                return cachedConfig[key];
            }
        }

        // Load from database and cache
        const dbConfig = await loadConfigFromDatabase();
        configCache.set(dbConfig);

        return dbConfig[key] ?? defaultValue;
    } catch (error) {
        LogErrorStack({ message: `Failed to get config value for ${key}`, error });
        return defaultValue;
    }
}

/**
 * Applies database overrides to a config section
 */
async function applyDatabaseOverrides<T extends { public: Record<string, any>; hidden: Record<string, any> }>(
    configSection: T,
    categoryPrefix: string
): Promise<T> {
    try {
        const result = JSON.parse(JSON.stringify(configSection)) as T;

        // Get all config keys for this category
        const allKeys = { ...configSection.public, ...configSection.hidden };

        for (const [key, defaultValue] of Object.entries(allKeys)) {
            const dbKey = `${categoryPrefix}.${key}`;
            const dbValue = await getConfigValue(dbKey, defaultValue);

            // Apply to the correct section (public or hidden)
            if (key in configSection.public) {
                result.public[key] = dbValue;
            } else if (key in configSection.hidden) {
                result.hidden[key] = dbValue;
            }
        }

        return result;
    } catch (error) {
        LogErrorStack({ message: `Failed to apply database overrides for ${categoryPrefix}`, error });
        return configSection;
    }
}

// Define the main configs object
const configs = {
    authConfig,
    frontendConfig,
    battleConfig,
    bankConfig,
    chatConfig,
    craftingConfig,
    casinoConfig,
    userConfig,
    jobsConfig,
    notificationConfig,
    levelGatesConfig,
    leaderboardsConfig,
    roguelikeConfig,
    uniqueItemsConfig,
    classesConfig,
    registrationCodes,
    questsConfig,
    shopConfig,
    skillsConfig,
    bountyConfig,
    profileConfig,
    missionConfig,
    shrineConfig,
    auctionConfig,
    gangConfig,
    infirmaryConfig,
    petConfig,
} as const;

/**
 * Creates database-aware config objects
 */
async function createDatabaseAwareConfigs() {
    try {
        return {
            authConfig: await applyDatabaseOverrides(authConfig, "auth"),
            frontendConfig: await applyDatabaseOverrides(frontendConfig, "frontend"),
            battleConfig: await applyDatabaseOverrides(battleConfig, "battle"),
            bankConfig: await applyDatabaseOverrides(bankConfig, "bank"),
            chatConfig: await applyDatabaseOverrides(chatConfig, "chat"),
            craftingConfig: await applyDatabaseOverrides(craftingConfig, "crafting"),
            casinoConfig: await applyDatabaseOverrides(casinoConfig, "casino"),
            userConfig: await applyDatabaseOverrides(userConfig, "user"),
            jobsConfig: await applyDatabaseOverrides(jobsConfig, "jobs"),
            notificationConfig: await applyDatabaseOverrides(notificationConfig, "notification"),
            levelGatesConfig: await applyDatabaseOverrides(levelGatesConfig, "levelGates"),
            leaderboardsConfig: await applyDatabaseOverrides(leaderboardsConfig, "leaderboards"),
            roguelikeConfig: await applyDatabaseOverrides(roguelikeConfig, "roguelike"),
            uniqueItemsConfig: await applyDatabaseOverrides(uniqueItemsConfig, "uniqueItems"),
            classesConfig: await applyDatabaseOverrides(classesConfig, "classes"),
            registrationCodes: await applyDatabaseOverrides(registrationCodes, "registrationCodes"),
            questsConfig: await applyDatabaseOverrides(questsConfig, "quests"),
            shopConfig: await applyDatabaseOverrides(shopConfig, "shop"),
            skillsConfig: await applyDatabaseOverrides(skillsConfig, "skills"),
            bountyConfig: await applyDatabaseOverrides(bountyConfig, "bounty"),
            profileConfig: await applyDatabaseOverrides(profileConfig, "profile"),
            missionConfig: await applyDatabaseOverrides(missionConfig, "mission"),
            shrineConfig: await applyDatabaseOverrides(shrineConfig, "shrine"),
            auctionConfig: await applyDatabaseOverrides(auctionConfig, "auction"),
            gangConfig: await applyDatabaseOverrides(gangConfig, "gang"),
            infirmaryConfig: await applyDatabaseOverrides(infirmaryConfig, "infirmary"),
            petConfig: await applyDatabaseOverrides(petConfig, "pet"),
        };
    } catch (error) {
        LogErrorStack({ message: "Failed to create database-aware configs, using defaults", error });
        return configs;
    }
}

type UnionToIntersection<U> = (U extends unknown ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

interface ConfigSection {
    public: Record<string, unknown>;
    hidden: Record<string, unknown>;
}
type ConfigsInput = typeof configs;

type AllMergedSections<T extends Record<string, unknown>> = {
    [K in keyof T]: T[K] extends ConfigSection ? T[K]["public"] & T[K]["hidden"] : never;
}[keyof T];

type PreciseFlatConfigType<T extends Record<string, unknown>> = {} & UnionToIntersection<AllMergedSections<T>>;

function createFlatConfig<T extends ConfigsInput>(cfg: T): PreciseFlatConfigType<T> {
    const flattened = Object.entries(cfg).reduce(
        (acc, [key, value]) => {
            const section = value as ConfigSection;
            Object.assign(acc, section.public, section.hidden);
            return acc;
        },
        {} as Record<string, unknown>
    );

    return flattened as PreciseFlatConfigType<T>;
}

type AllPublicSections<T extends Record<string, unknown>> = {
    [K in keyof T]: T[K] extends ConfigSection ? T[K]["public"] : never;
}[keyof T];

type PrecisePublicConfigType<T extends Record<string, unknown>> = {} & UnionToIntersection<AllPublicSections<T>>;

function createPublicConfig<T extends ConfigsInput>(cfg: T): PrecisePublicConfigType<T> {
    const flattened = Object.entries(cfg).reduce(
        (acc, [key, value]) => {
            const section = value as ConfigSection;
            Object.assign(acc, section.public);
            return acc;
        },
        {} as Record<string, unknown>
    );

    return flattened as PrecisePublicConfigType<T>;
}

// Export individual configs
export {
    authConfig,
    frontendConfig,
    battleConfig,
    bankConfig,
    chatConfig,
    craftingConfig,
    casinoConfig,
    userConfig,
    jobsConfig,
    notificationConfig,
    levelGatesConfig,
    leaderboardsConfig,
    roguelikeConfig,
    uniqueItemsConfig,
    classesConfig,
    registrationCodes,
    questsConfig,
    shopConfig,
    skillsConfig,
    bountyConfig,
    profileConfig,
    missionConfig,
    shrineConfig,
    auctionConfig,
    gangConfig,
    infirmaryConfig,
    petConfig,
};

// Cache for database-aware configs
let databaseAwareConfigsCache: any = null;
let lastConfigLoad = 0;
const CONFIG_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Gets database-aware configs with caching
 */
async function getDatabaseAwareConfigs() {
    const now = Date.now();
    if (databaseAwareConfigsCache && now - lastConfigLoad < CONFIG_CACHE_DURATION) {
        return databaseAwareConfigsCache;
    }

    databaseAwareConfigsCache = await createDatabaseAwareConfigs();
    lastConfigLoad = now;
    return databaseAwareConfigsCache;
}

/**
 * Creates flat config with database overrides
 */
async function createDatabaseAwareFlatConfig() {
    const dbConfigs = await getDatabaseAwareConfigs();
    return createFlatConfig(dbConfigs);
}

/**
 * Creates public config with database overrides
 */
async function createDatabaseAwarePublicConfig() {
    const dbConfigs = await getDatabaseAwareConfigs();
    return createPublicConfig(dbConfigs);
}

// Export the database-aware gameConfig
export const gameConfig = {
    ...configs,
    // Add method to get database-aware configs
    async getDatabaseAware() {
        return await getDatabaseAwareConfigs();
    },
};

// For backward compatibility, export the default configs synchronously
// but also provide async versions that include database overrides
const flatConfig = createFlatConfig(configs);
export default flatConfig;
export const publicConfig = createPublicConfig(configs);

// Export async versions that include database overrides
export const getDatabaseAwareFlatConfig = createDatabaseAwareFlatConfig;
export const getDatabaseAwarePublicConfig = createDatabaseAwarePublicConfig;

// Export utility functions for managing config cache
export const clearConfigCache = () => {
    configCache.set({});
    databaseAwareConfigsCache = null;
    lastConfigLoad = 0;
};

/**
 * Seeds the database with default configuration values
 * This is useful for initial setup or when adding new config options
 */
export async function seedDatabaseConfig() {
    try {
        const configEntries = [];

        // Helper function to add config entries
        const addConfigEntries = (configSection: any, categoryPrefix: string, isPublic: boolean) => {
            const section = isPublic ? configSection.public : configSection.hidden;
            for (const [key, value] of Object.entries(section)) {
                configEntries.push({
                    key: `${categoryPrefix}.${key}`,
                    value: value,
                    category: categoryPrefix,
                    isPublic: isPublic,
                });
            }
        };

        // Add all config sections
        addConfigEntries(authConfig, "auth", true);
        addConfigEntries(authConfig, "auth", false);
        addConfigEntries(frontendConfig, "frontend", true);
        addConfigEntries(frontendConfig, "frontend", false);
        addConfigEntries(battleConfig, "battle", true);
        addConfigEntries(battleConfig, "battle", false);
        addConfigEntries(bankConfig, "bank", true);
        addConfigEntries(bankConfig, "bank", false);
        addConfigEntries(chatConfig, "chat", true);
        addConfigEntries(chatConfig, "chat", false);
        addConfigEntries(craftingConfig, "crafting", true);
        addConfigEntries(craftingConfig, "crafting", false);
        addConfigEntries(casinoConfig, "casino", true);
        addConfigEntries(casinoConfig, "casino", false);
        addConfigEntries(userConfig, "user", true);
        addConfigEntries(userConfig, "user", false);
        addConfigEntries(jobsConfig, "jobs", true);
        addConfigEntries(jobsConfig, "jobs", false);
        addConfigEntries(notificationConfig, "notification", true);
        addConfigEntries(notificationConfig, "notification", false);
        addConfigEntries(levelGatesConfig, "levelGates", true);
        addConfigEntries(levelGatesConfig, "levelGates", false);
        addConfigEntries(leaderboardsConfig, "leaderboards", true);
        addConfigEntries(leaderboardsConfig, "leaderboards", false);
        addConfigEntries(roguelikeConfig, "roguelike", true);
        addConfigEntries(roguelikeConfig, "roguelike", false);
        addConfigEntries(uniqueItemsConfig, "uniqueItems", true);
        addConfigEntries(uniqueItemsConfig, "uniqueItems", false);
        addConfigEntries(classesConfig, "classes", true);
        addConfigEntries(classesConfig, "classes", false);
        addConfigEntries(registrationCodes, "registrationCodes", true);
        addConfigEntries(registrationCodes, "registrationCodes", false);
        addConfigEntries(questsConfig, "quests", true);
        addConfigEntries(questsConfig, "quests", false);
        addConfigEntries(shopConfig, "shop", true);
        addConfigEntries(shopConfig, "shop", false);
        addConfigEntries(skillsConfig, "skills", true);
        addConfigEntries(skillsConfig, "skills", false);
        addConfigEntries(bountyConfig, "bounty", true);
        addConfigEntries(bountyConfig, "bounty", false);
        addConfigEntries(profileConfig, "profile", true);
        addConfigEntries(profileConfig, "profile", false);
        addConfigEntries(missionConfig, "mission", true);
        addConfigEntries(missionConfig, "mission", false);
        addConfigEntries(shrineConfig, "shrine", true);
        addConfigEntries(shrineConfig, "shrine", false);
        addConfigEntries(auctionConfig, "auction", true);
        addConfigEntries(auctionConfig, "auction", false);
        addConfigEntries(gangConfig, "gang", true);
        addConfigEntries(gangConfig, "gang", false);
        addConfigEntries(infirmaryConfig, "infirmary", true);
        addConfigEntries(infirmaryConfig, "infirmary", false);
        addConfigEntries(petConfig, "pet", true);
        addConfigEntries(petConfig, "pet", false);

        // Use upsert to avoid conflicts
        for (const entry of configEntries) {
            await db.game_config.upsert({
                where: { key: entry.key },
                update: {
                    value: entry.value,
                    category: entry.category,
                    isPublic: entry.isPublic,
                },
                create: entry,
            });
        }

        logger.info(`Seeded ${configEntries.length} configuration entries to database`);

        // Clear cache after seeding
        clearConfigCache();

        return configEntries.length;
    } catch (error) {
        LogErrorStack({ message: "Failed to seed database config", error });
        throw error;
    }
}
